"""
示例：如何使用优化后的 JSON model 在 LangGraph 中进行结构化输出

这个示例展示了如何使用 DeepSeek 的 JSON Output 功能来确保模型输出合法的 JSON 字符串。
"""

import json
from backend.core.llm import json_model_with_prompt
from langchain_core.messages import HumanMessage

def parse_exam_question_answer():
    """
    解析考试题目和答案的示例
    """
    # 系统提示已经在 json_model_with_prompt 中配置
    user_prompt = """
    Please parse the following exam text and extract the "question" and "answer" in JSON format.
    
    INPUT: Which is the longest river in the world? The Nile River.
    
    Expected JSON format:
    {
        "question": "extracted question here",
        "answer": "extracted answer here"
    }
    """
    
    # 使用 JSON model 进行推理
    response = json_model_with_prompt.invoke([
        HumanMessage(content=user_prompt)
    ])
    
    # 解析 JSON 响应
    try:
        result = json.loads(response.content)
        print("解析成功:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return result
    except json.JSONDecodeError as e:
        print(f"JSON 解析失败: {e}")
        print(f"原始响应: {response.content}")
        return None

def extract_structured_data():
    """
    提取结构化数据的示例
    """
    user_prompt = """
    Extract the following information from this text in JSON format:
    
    "John Smith is a 30-year-old software engineer living in New York. He works at TechCorp and earns $120,000 per year. His <NAME_EMAIL> and his phone number is ******-0123."
    
    Please extract: name, age, profession, location, company, salary, email, phone in JSON format.
    """
    
    response = json_model_with_prompt.invoke([
        HumanMessage(content=user_prompt)
    ])
    
    try:
        result = json.loads(response.content)
        print("提取的结构化数据:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return result
    except json.JSONDecodeError as e:
        print(f"JSON 解析失败: {e}")
        print(f"原始响应: {response.content}")
        return None

def create_langgraph_compatible_node():
    """
    创建一个 LangGraph 兼容的节点函数
    """
    def json_processing_node(state):
        """
        LangGraph 节点函数，处理输入并返回 JSON 格式的结果
        """
        input_text = state.get("input_text", "")
        
        prompt = f"""
        Process the following input and return a structured JSON response with:
        - summary: a brief summary of the input
        - key_points: an array of key points
        - sentiment: positive/negative/neutral
        - word_count: number of words
        
        Input: {input_text}
        
        Return the result in JSON format.
        """
        
        response = json_model_with_prompt.invoke([
            HumanMessage(content=prompt)
        ])
        
        try:
            result = json.loads(response.content)
            return {"processed_data": result, "status": "success"}
        except json.JSONDecodeError as e:
            return {"error": str(e), "status": "failed", "raw_response": response.content}
    
    return json_processing_node

if __name__ == "__main__":
    print("=== 示例 1: 解析考试题目和答案 ===")
    parse_exam_question_answer()
    
    print("\n=== 示例 2: 提取结构化数据 ===")
    extract_structured_data()
    
    print("\n=== 示例 3: LangGraph 节点函数 ===")
    node_func = create_langgraph_compatible_node()
    test_state = {"input_text": "This is a great product! I love using it every day. It has amazing features and excellent customer support."}
    result = node_func(test_state)
    print("LangGraph 节点处理结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
